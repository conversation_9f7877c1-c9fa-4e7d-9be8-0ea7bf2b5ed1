import React from 'react';
import { Select } from 'antd';
import * as styles from './index.module.less';

interface Option {
  value: string;
  label: string;
}

interface CustomSelectProps {
  value: string;
  options: Option[];
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  value,
  options,
  onChange,
  className = '',
  placeholder = '请选择'
}) => {
  return (
    <div className={`${styles.customSelect} ${className}`}>
      <Select
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        options={options}
        style={{
          width: '100%',
          height: '32px'
        }}
        size="small"
        popupClassName="language-selector-dropdown"
        getPopupContainer={(triggerNode) => {
          // 确保下拉框渲染在 Shadow DOM 内部
          // 找到最近的 Shadow Root 容器
          let container = triggerNode.parentElement;
          while (container && !container.classList.contains('web-assistant-container')) {
            container = container.parentElement;
          }
          return container || document.body;
        }}
      />
    </div>
  );
};

export default CustomSelect;
