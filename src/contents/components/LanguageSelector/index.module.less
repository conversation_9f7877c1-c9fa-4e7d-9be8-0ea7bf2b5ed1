.languageSelector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin: 4px 0;
}

.selectInput {
  /* 重置默认样式 */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;

  /* 基础样式 */
  flex: 1;
  min-width: 80px;
  height: 32px;
  padding: 4px 24px 4px 11px;
  font-size: 14px;
  line-height: 1.5714285714285714;
  color: rgba(0, 0, 0, 0.88);
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  outline: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

  /* 自定义下拉箭头 */
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;

  /* 字体设置 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

  /* 优化下拉框样式 - 针对支持的浏览器 */
  /* Webkit浏览器（Chrome, Safari）的下拉框优化 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

.selectInput:hover {
  border-color: #4096ff;
}

.selectInput:focus {
  border-color: #4096ff;
  box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.2);
}

/* 打开状态的样式优化 */
.selectInput:focus,
.selectInput:active {
  /* 下拉箭头旋转效果 */
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%234096ff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 12l4-4 4 4'/%3e%3c/svg%3e");
}

.selectInput:disabled {
  color: rgba(0, 0, 0, 0.25);
  background-color: rgba(0, 0, 0, 0.04);
  border-color: #d9d9d9;
  cursor: not-allowed;
}

/* 选项样式优化 - 尽可能的样式定制 */
.selectInput option {
  /* 基础样式 */
  padding: 8px 12px;
  color: rgba(0, 0, 0, 0.88);
  background-color: #ffffff;
  font-size: 14px;
  line-height: 22px;
  min-height: 32px;

  /* 字体设置 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  font-weight: 400;

  /* 边框和间距 */
  border: none;
  margin: 0;

  /* 过渡效果 */
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* 悬停效果 - 在支持的浏览器中生效 */
.selectInput option:hover {
  background-color: rgba(64, 150, 255, 0.06) !important;
  color: rgba(0, 0, 0, 0.88) !important;
}

/* 选中状态样式 */
.selectInput option:checked,
.selectInput option:selected {
  background-color: #e6f4ff !important;
  color: #1677ff !important;
  font-weight: 600;
}

/* 焦点状态 */
.selectInput option:focus {
  background-color: rgba(64, 150, 255, 0.06) !important;
  outline: none;
}

/* 禁用状态 */
.selectInput option:disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  background-color: rgba(0, 0, 0, 0.04) !important;
  cursor: not-allowed;
}

/* Firefox 特定样式 */
@-moz-document url-prefix() {
  .selectInput option {
    background-color: #ffffff;
    color: rgba(0, 0, 0, 0.88);
    padding: 8px 12px;
  }

  .selectInput option:hover {
    background-color: rgba(64, 150, 255, 0.06);
  }

  .selectInput option:checked {
    background-color: #e6f4ff;
    color: #1677ff;
  }
}

.arrowIcon {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  user-select: none;
  pointer-events: none;
  margin: 0 4px;
}

/* 自定义Select组件样式 */
.customSelect {
  position: relative;
  flex: 1;
  min-width: 80px;
}

.selectTrigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  padding: 4px 11px;
  font-size: 14px;
  line-height: 1.5714285714285714;
  color: rgba(0, 0, 0, 0.88);
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  outline: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  user-select: none;

  /* 字体设置 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

.selectTrigger:hover {
  border-color: #4096ff;
}

.selectTrigger:focus,
.customSelect.open .selectTrigger {
  border-color: #4096ff;
  box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.2);
}

.selectValue {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.selectArrow {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.25);
  transition: transform 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  margin-left: 8px;
}

.selectArrow.rotated {
  transform: rotate(180deg);
  color: #4096ff;
}

.selectDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1050;
  margin-top: 4px;
  padding: 4px 0;
  background-color: #ffffff;
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
              0 3px 6px -4px rgba(0, 0, 0, 0.12),
              0 9px 28px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(5, 5, 5, 0.06);
  max-height: 200px;
  overflow-y: auto;

  /* 滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

.selectOption {
  position: relative;
  display: block;
  min-height: 32px;
  padding: 5px 12px;
  color: rgba(0, 0, 0, 0.88);
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  cursor: pointer;
  transition: background-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  /* 字体设置 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

.selectOption:hover,
.selectOption.highlighted {
  background-color: rgba(0, 0, 0, 0.04);
}

.selectOption.selected {
  color: #1677ff;
  font-weight: 600;
  background-color: #e6f4ff;
}

.selectOption.selected.highlighted {
  background-color: #bae0ff;
}

/* Ant Design Select 样式覆盖 - 适配 Shadow DOM */
.customSelect {
  :global(.ant-select) {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;

    .ant-select-selector {
      border-radius: 6px !important;
      border-color: #d9d9d9 !important;
      transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
    }

    &:hover .ant-select-selector {
      border-color: #4096ff !important;
    }

    &.ant-select-focused .ant-select-selector {
      border-color: #4096ff !important;
      box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.2) !important;
    }

    .ant-select-selection-item {
      color: rgba(0, 0, 0, 0.88) !important;
      font-size: 14px !important;
      line-height: 1.5714285714285714 !important;
    }

    .ant-select-arrow {
      color: rgba(0, 0, 0, 0.25) !important;
    }

    &.ant-select-open .ant-select-arrow {
      color: #4096ff !important;
    }
  }
}

/* 全局下拉框样式 - 用于 Shadow DOM 内的下拉框 */
:global(.language-selector-dropdown) {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif !important;

  .ant-select-item {
    font-size: 14px !important;
    line-height: 22px !important;
    min-height: 32px !important;
    padding: 5px 12px !important;
    color: rgba(0, 0, 0, 0.88) !important;
    transition: background-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
  }

  .ant-select-item-option-active {
    background-color: rgba(0, 0, 0, 0.04) !important;
  }

  .ant-select-item-option-selected {
    color: #1677ff !important;
    font-weight: 600 !important;
    background-color: #e6f4ff !important;
  }

  .ant-select-item-option-selected.ant-select-item-option-active {
    background-color: #bae0ff !important;
  }
}
